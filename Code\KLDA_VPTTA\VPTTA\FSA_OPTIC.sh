#!/bin/bash

#Please modify the following roots to yours.
dataset_root=/opt/data/private/zjw/Data/Fundus
model_root=/opt/data/private/zjw/Data/models
path_save_log=/opt/data/private/zjw/VPTTA-main/OPTIC/logs/

#Dataset [RIM_ONE_r3, REFUGE, ORIGA, REFUGE_Valid, Drishti_GS]
Source=RIM_ONE_r3

#FSA Hyperparameters
rff_dim=512
rbf_sigma=1.0

#Command
cd OPTIC
CUDA_VISIBLE_DEVICES=0 python fsa.py \
--dataset_root $dataset_root --model_root $model_root --path_save_log $path_save_log \
--Source_Dataset $Source \
--rff_dim $rff_dim --rbf_sigma $rbf_sigma
