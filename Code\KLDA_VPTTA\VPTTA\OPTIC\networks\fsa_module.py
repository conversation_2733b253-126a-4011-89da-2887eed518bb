import torch
import torch.nn as nn
import numpy as np
from torch.nn import functional as F


class RFFTransform(nn.Module):
    """Random Fourier Features transformation"""
    def __init__(self, input_dim, rff_dim, rbf_sigma, device):
        super(RFFTransform, self).__init__()
        self.input_dim = input_dim
        self.rff_dim = rff_dim
        self.rbf_sigma = rbf_sigma
        self.device = device
        
        # Initialize fixed RFF parameters
        torch.manual_seed(42)  # Fixed seed for reproducibility
        self.register_buffer('omega', torch.normal(
            0, 
            torch.sqrt(torch.tensor(2 * self.rbf_sigma, dtype=torch.float32)),
            (self.input_dim, self.rff_dim)
        ))
        self.register_buffer('b', torch.rand(self.rff_dim) * 2 * torch.pi)
        
    def forward(self, x):
        """
        Args:
            x: Input tensor of shape (batch_size, input_dim)
        Returns:
            Transformed tensor of shape (batch_size, rff_dim)
        """
        scaling_factor = torch.sqrt(torch.tensor(2.0 / self.rff_dim, dtype=torch.float32, device=self.device))
        return scaling_factor * torch.cos(x @ self.omega + self.b)


class SourceDomainStats:
    """Compute and store source domain statistics"""
    def __init__(self, rff_dim, device):
        self.rff_dim = rff_dim
        self.device = device
        self.mu_source = None
        self.sigma_source = None
        self.sigma_source_sqrt = None
        
    def compute_stats(self, features_list):
        """
        Compute source domain statistics from a list of RFF-transformed features
        Args:
            features_list: List of tensors, each of shape (batch_size, rff_dim)
        """
        # Concatenate all features
        all_features = torch.cat(features_list, dim=0)  # (total_samples, rff_dim)
        
        # Compute mean
        self.mu_source = torch.mean(all_features, dim=0)  # (rff_dim,)
        
        # Compute covariance matrix
        centered_features = all_features - self.mu_source.unsqueeze(0)
        self.sigma_source = torch.mm(centered_features.t(), centered_features) / (all_features.size(0) - 1)
        
        # Compute matrix square root for coloring transform
        # Use eigendecomposition: Σ = UΛU^T, Σ^(1/2) = UΛ^(1/2)U^T
        eigenvals, eigenvecs = torch.symeig(self.sigma_source, eigenvectors=True)
        eigenvals = torch.clamp(eigenvals, min=1e-6)  # Numerical stability
        self.sigma_source_sqrt = torch.mm(torch.mm(eigenvecs, torch.diag(torch.sqrt(eigenvals))), eigenvecs.t())


class TargetDomainStats:
    """Online incremental computation of target domain statistics"""
    def __init__(self, rff_dim, device):
        self.rff_dim = rff_dim
        self.device = device
        self.mu_target = torch.zeros(rff_dim, device=device)
        self.sigma_target = torch.eye(rff_dim, device=device) * 1e-3  # Small identity for initialization
        self.sigma_target_inv_sqrt = None
        self.n_samples = 0
        
    def update(self, features):
        """
        Update target domain statistics with new features
        Args:
            features: Tensor of shape (batch_size, rff_dim)
        """
        batch_size = features.size(0)
        
        # Update sample count
        prev_n = self.n_samples
        self.n_samples += batch_size
        
        # Update mean incrementally
        batch_mean = torch.mean(features, dim=0)
        if prev_n == 0:
            self.mu_target = batch_mean
        else:
            self.mu_target = (prev_n * self.mu_target + batch_size * batch_mean) / self.n_samples
        
        # Update covariance incrementally
        centered_features = features - self.mu_target.unsqueeze(0)
        batch_cov = torch.mm(centered_features.t(), centered_features)
        self.sigma_target = (prev_n * self.sigma_target + batch_cov) / self.n_samples
        
        # Compute inverse square root for whitening transform
        eigenvals, eigenvecs = torch.symeig(self.sigma_target, eigenvectors=True)
        eigenvals = torch.clamp(eigenvals, min=1e-6)  # Numerical stability
        self.sigma_target_inv_sqrt = torch.mm(torch.mm(eigenvecs, torch.diag(1.0 / torch.sqrt(eigenvals))), eigenvecs.t())


class WhiteningColoring:
    """Whitening and coloring transformation for feature alignment"""
    def __init__(self, device):
        self.device = device
        
    def transform(self, features, mu_target, sigma_target_inv_sqrt, mu_source, sigma_source_sqrt):
        """
        Apply whitening-coloring transformation
        Args:
            features: Input features (batch_size, rff_dim)
            mu_target: Target domain mean (rff_dim,)
            sigma_target_inv_sqrt: Target domain covariance inverse square root (rff_dim, rff_dim)
            mu_source: Source domain mean (rff_dim,)
            sigma_source_sqrt: Source domain covariance square root (rff_dim, rff_dim)
        Returns:
            Transformed features (batch_size, rff_dim)
        """
        # Check if matrices are available
        if sigma_target_inv_sqrt is None or sigma_source_sqrt is None:
            return features

        # Whitening: remove target domain statistics
        centered_features = features - mu_target.unsqueeze(0)
        whitened_features = torch.mm(centered_features, sigma_target_inv_sqrt.t())

        # Coloring: apply source domain statistics
        colored_features = torch.mm(whitened_features, sigma_source_sqrt.t()) + mu_source.unsqueeze(0)

        return colored_features


class FSAModule(nn.Module):
    """Complete FSA module integrating all components"""
    def __init__(self, input_dim, rff_dim, rbf_sigma, device):
        super(FSAModule, self).__init__()
        self.input_dim = input_dim
        self.rff_dim = rff_dim
        self.device = device

        # Initialize components
        self.rff_transform = RFFTransform(input_dim, rff_dim, rbf_sigma, device)
        self.source_stats = SourceDomainStats(rff_dim, device)
        self.target_stats = TargetDomainStats(rff_dim, device)
        self.whitening_coloring = WhiteningColoring(device)

    def compute_source_stats(self, source_features_list):
        """Compute source domain statistics (called once during initialization)"""
        # Transform source features with RFF
        rff_features_list = []
        for features in source_features_list:
            # Flatten spatial dimensions: (B, C, H, W) -> (B, C*H*W)
            batch_size = features.size(0)
            flattened_features = features.view(batch_size, -1)
            rff_features = self.rff_transform(flattened_features)
            rff_features_list.append(rff_features)

        self.source_stats.compute_stats(rff_features_list)

    def forward(self, features):
        """
        Forward pass for test-time adaptation
        Args:
            features: Input features (B, C, H, W)
        Returns:
            Adapted features (B, C, H, W)
        """
        original_shape = features.shape
        batch_size = features.size(0)

        # Flatten spatial dimensions
        flattened_features = features.view(batch_size, -1)

        # Apply RFF transformation
        rff_features = self.rff_transform(flattened_features)

        # Update target domain statistics
        self.target_stats.update(rff_features.detach())

        # Apply whitening-coloring transformation
        if self.target_stats.n_samples > 0 and self.source_stats.mu_source is not None:
            adapted_rff_features = self.whitening_coloring.transform(
                rff_features,
                self.target_stats.mu_target,
                self.target_stats.sigma_target_inv_sqrt,
                self.source_stats.mu_source,
                self.source_stats.sigma_source_sqrt
            )
        else:
            adapted_rff_features = rff_features

        # For simplicity, we skip the inverse RFF transformation and directly work in RFF space
        # We'll modify the decoder to accept RFF-transformed features
        # For now, we use a simple linear projection back to original space
        if not hasattr(self, 'inverse_projection'):
            self.inverse_projection = nn.Linear(self.rff_dim, self.input_dim, bias=False).to(self.device)
            # Initialize with pseudo-inverse approximation
            with torch.no_grad():
                # Use a simple random initialization scaled appropriately
                self.inverse_projection.weight.data.normal_(0, 0.01)

        adapted_features = self.inverse_projection(adapted_rff_features)

        # Reshape back to original spatial dimensions
        adapted_features = adapted_features.view(original_shape)

        return adapted_features
