import os
import torch
import numpy as np
import argparse, sys, datetime
from config import Logger
from torch.autograd import Variable
from utils.metrics import calculate_metrics
from networks.ResUnet_FSA import ResUnet
from torch.utils.data import DataLoader
from dataloaders.OPTIC_dataloader import OPTIC_dataset
from dataloaders.transform import collate_fn_wo_transform
from dataloaders.convert_csv_to_list import convert_labeled_list


torch.set_num_threads(1)


class FSA:
    def __init__(self, config):
        # Save Log
        time_now = datetime.datetime.now().__format__("%Y%m%d_%H%M%S_%f")
        log_root = os.path.join(config.path_save_log, 'FSA')
        if not os.path.exists(log_root):
            os.makedirs(log_root)
        log_path = os.path.join(log_root, time_now + '.log')
        sys.stdout = Logger(log_path, sys.stdout)

        # Data Loading for Target Domain
        target_test_csv = []
        for target in config.Target_Dataset:
            if target != 'REFUGE_Valid':
                target_test_csv.append(target + '_train.csv')
                target_test_csv.append(target + '_test.csv')
            else:
                target_test_csv.append(target + '.csv')
        ts_img_list, ts_label_list = convert_labeled_list(config.dataset_root, target_test_csv)
        target_test_dataset = OPTIC_dataset(config.dataset_root, ts_img_list, ts_label_list,
                                            config.image_size, img_normalize=True)
        self.target_test_loader = DataLoader(dataset=target_test_dataset,
                                             batch_size=config.batch_size,
                                             shuffle=False,
                                             pin_memory=True,
                                             drop_last=False,
                                             collate_fn=collate_fn_wo_transform,
                                             num_workers=config.num_workers)

        # Data Loading for Source Domain (for computing source statistics)
        source_train_csv = [config.Source_Dataset + '_train.csv']
        src_img_list, src_label_list = convert_labeled_list(config.dataset_root, source_train_csv)
        source_train_dataset = OPTIC_dataset(config.dataset_root, src_img_list, src_label_list,
                                             config.image_size, img_normalize=True)
        self.source_train_loader = DataLoader(dataset=source_train_dataset,
                                              batch_size=config.batch_size,
                                              shuffle=False,
                                              pin_memory=True,
                                              drop_last=False,
                                              collate_fn=collate_fn_wo_transform,
                                              num_workers=config.num_workers)

        self.image_size = config.image_size

        # Model
        self.load_model = os.path.join(config.model_root, str(config.Source_Dataset))  # Pre-trained Source Model
        self.backbone = config.backbone
        self.in_ch = config.in_ch
        self.out_ch = config.out_ch

        # FSA Parameters
        self.rff_dim = config.rff_dim
        self.rbf_sigma = config.rbf_sigma

        # GPU
        self.device = config.device

        # Initialize the pre-trained model
        self.build_model()

        # Compute source domain statistics
        self.compute_source_statistics()

        # Print Information
        for arg, value in vars(config).items():
            print(f"{arg}: {value}")
        print('***' * 20)

    def build_model(self):
        self.model = ResUnet(resnet=self.backbone, num_classes=self.out_ch, pretrained=False, 
                            rff_dim=self.rff_dim, rbf_sigma=self.rbf_sigma, device=self.device).to(self.device)
        checkpoint = torch.load(os.path.join(self.load_model, 'last-Res_Unet.pth'))
        # Load only the ResNet backbone weights, ignore FSA module
        model_dict = self.model.state_dict()
        pretrained_dict = {k: v for k, v in checkpoint.items() if k in model_dict and 'fsa_module' not in k}
        model_dict.update(pretrained_dict)
        self.model.load_state_dict(model_dict)

    def compute_source_statistics(self):
        """Compute source domain statistics using source training data"""
        print("Computing source domain statistics...")
        self.model.eval()
        
        source_features_list = []
        with torch.no_grad():
            for batch, data in enumerate(self.source_train_loader):
                if batch >= 100:  # Limit to first 100 batches for efficiency
                    break
                    
                x = data['data']
                x = torch.from_numpy(x).to(dtype=torch.float32)
                x = Variable(x).to(self.device)
                
                # Extract features from layer4 (before FSA)
                encoder_features, _ = self.model.res(x)
                encoder_features = torch.relu(encoder_features)
                source_features_list.append(encoder_features.cpu())
                
                if batch % 20 == 0:
                    print(f"Processed {batch + 1} batches for source statistics")
        
        # Move features back to GPU and compute statistics
        source_features_list = [f.to(self.device) for f in source_features_list]
        self.model.fsa_module.compute_source_stats(source_features_list)
        print("Source domain statistics computed successfully!")

    def run(self):
        metric_dict = ['Disc_Dice', 'Disc_ASD', 'Cup_Dice', 'Cup_ASD']

        # Valid on Target
        metrics_test = [[], [], [], []]

        self.model.eval()
        for batch, data in enumerate(self.target_test_loader):
            x, y = data['data'], data['mask']
            x = torch.from_numpy(x).to(dtype=torch.float32)
            y = torch.from_numpy(y).to(dtype=torch.float32)

            x, y = Variable(x).to(self.device), Variable(y).to(self.device)

            # Forward pass with FSA adaptation
            with torch.no_grad():
                pred_logit, fea, head_input = self.model(x)

            # Calculate the metrics
            seg_output = torch.sigmoid(pred_logit)
            metrics = calculate_metrics(seg_output.detach().cpu(), y.detach().cpu())
            for i in range(len(metrics)):
                assert isinstance(metrics[i], list), "The metrics value is not list type."
                metrics_test[i] += metrics[i]

            if batch % 50 == 0:
                print(f"Processed {batch + 1} test batches")

        test_metrics_y = np.mean(metrics_test, axis=1)
        print_test_metric_mean = {}
        for i in range(len(test_metrics_y)):
            print_test_metric_mean[metric_dict[i]] = test_metrics_y[i]
        print("Test Metrics: ", print_test_metric_mean)
        print('Mean Dice:', (print_test_metric_mean['Disc_Dice'] + print_test_metric_mean['Cup_Dice']) / 2)


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    # Dataset
    parser.add_argument('--Source_Dataset', type=str, default='RIM_ONE_r3',
                        help='RIM_ONE_r3/REFUGE/ORIGA/REFUGE_Valid/Drishti_GS')
    parser.add_argument('--Target_Dataset', type=list)

    parser.add_argument('--num_workers', type=int, default=8)
    parser.add_argument('--image_size', type=int, default=512)

    # Model
    parser.add_argument('--backbone', type=str, default='resnet34', help='resnet34/resnet50')
    parser.add_argument('--in_ch', type=int, default=3)
    parser.add_argument('--out_ch', type=int, default=2)

    # Training
    parser.add_argument('--batch_size', type=int, default=1)

    # FSA Hyperparameters
    parser.add_argument('--rff_dim', type=int, default=512)
    parser.add_argument('--rbf_sigma', type=float, default=1.0)

    # Path
    parser.add_argument('--path_save_log', type=str, default='./logs')
    parser.add_argument('--model_root', type=str, default='./models')
    parser.add_argument('--dataset_root', type=str, default='/media/userdisk0/zychen/Datasets/Fundus')

    # Cuda (default: the first available device)
    parser.add_argument('--device', type=str, default='cuda:0')

    config = parser.parse_args()

    config.Target_Dataset = ['RIM_ONE_r3', 'REFUGE', 'ORIGA', 'REFUGE_Valid', 'Drishti_GS']
    config.Target_Dataset.remove(config.Source_Dataset)

    TTA = FSA(config)
    TTA.run()
