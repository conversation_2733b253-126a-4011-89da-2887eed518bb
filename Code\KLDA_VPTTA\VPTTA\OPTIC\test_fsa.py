#!/usr/bin/env python3
"""
Test script for FSA implementation
"""

import sys
import os
sys.path.append('.')

def test_imports():
    """Test if all modules can be imported"""
    print("Testing imports...")
    try:
        import torch
        print(f"✓ PyTorch {torch.__version__} imported successfully")
        
        from networks.fsa_module import FSAModule, RFFTransform, SourceDomainStats, TargetDomainStats, WhiteningColoring
        print("✓ FSA module components imported successfully")
        
        from networks.ResUnet_FSA import ResUnet
        print("✓ ResUnet_FSA imported successfully")
        
        return True
    except Exception as e:
        print(f"✗ Import error: {e}")
        return False

def test_fsa_module():
    """Test FSA module functionality"""
    print("\nTesting FSA module...")
    try:
        import torch
        device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {device}")
        
        from networks.fsa_module import FSAModule
        
        # Test parameters
        input_dim = 512 * 16 * 16  # 131072
        rff_dim = 512
        rbf_sigma = 1.0
        
        # Create FSA module
        fsa = FSAModule(input_dim, rff_dim, rbf_sigma, device)
        print("✓ FSA module created successfully")
        
        # Test with dummy data
        batch_size = 2
        dummy_features = torch.randn(batch_size, 512, 16, 16).to(device)
        print(f"Input shape: {dummy_features.shape}")
        
        # Test forward pass
        output = fsa(dummy_features)
        print(f"Output shape: {output.shape}")
        
        if output.shape == dummy_features.shape:
            print("✓ FSA module forward pass successful")
            return True
        else:
            print("✗ Output shape mismatch")
            return False
            
    except Exception as e:
        print(f"✗ FSA module test error: {e}")
        return False

def test_resunet_fsa():
    """Test ResUnet_FSA model"""
    print("\nTesting ResUnet_FSA...")
    try:
        import torch
        device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
        
        from networks.ResUnet_FSA import ResUnet
        
        # Create model
        model = ResUnet(resnet='resnet34', num_classes=2, pretrained=False, 
                       rff_dim=512, rbf_sigma=1.0, device=device).to(device)
        print("✓ ResUnet_FSA model created successfully")
        
        # Test with dummy input
        dummy_input = torch.randn(1, 3, 512, 512).to(device)
        print(f"Input shape: {dummy_input.shape}")
        
        model.eval()
        with torch.no_grad():
            seg_output, sfs, head_input = model(dummy_input)
        
        print(f"Segmentation output shape: {seg_output.shape}")
        print(f"Number of skip features: {len(sfs)}")
        print(f"Head input shape: {head_input.shape}")
        
        if seg_output.shape == (1, 2, 512, 512):
            print("✓ ResUnet_FSA forward pass successful")
            return True
        else:
            print("✗ Output shape mismatch")
            return False
            
    except Exception as e:
        print(f"✗ ResUnet_FSA test error: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 50)
    print("FSA Implementation Test Suite")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_fsa_module,
        test_resunet_fsa,
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("Test Results:")
    print("=" * 50)
    
    test_names = [test.__name__ for test in tests]
    for name, result in zip(test_names, results):
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{name}: {status}")
    
    total_passed = sum(results)
    total_tests = len(results)
    print(f"\nTotal: {total_passed}/{total_tests} tests passed")
    
    if total_passed == total_tests:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1

if __name__ == "__main__":
    exit(main())
